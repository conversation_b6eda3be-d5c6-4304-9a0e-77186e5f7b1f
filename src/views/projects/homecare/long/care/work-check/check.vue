<template>
    <div v-loading="loading">
        <div class="box-wrapper ut-body" :style="{ height: `${height}px` }">
            <el-row type="flex">
                <el-col :span="10">
                    <div class="customer-info">
                        <el-descriptions :column="9" border>
                            <el-descriptions-item span="3" label="客户姓名">{{ form.name }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="性别">
                                <span v-if="form.sex === 1">男</span>
                                <span v-else-if="form.sex === 2">女</span>
                            </el-descriptions-item>
                            <el-descriptions-item span="3" label="手机号">{{ form.phone }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="证件号码">{{ form.idcard }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="分组名称">{{ form.groupName }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="客户员">{{ form.attendantName }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="护理日期">{{ form.workDate }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="排班时长">{{ form.schedulingDuration }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="实际时长">{{ form.totalDuration }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="签到时间">{{ form.checkInTime }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="签退时间">{{ form.checkOutTime }}</el-descriptions-item>
                        </el-descriptions>
                    </div>
                    <div class="project-section">
                        <project-list :projects="form.projects" :parent-data="childData" @update:projects="updateProjects" @reset="resetProjects" />
                    </div>
                </el-col>
                <el-col :span="14">
                    <data-set :datas="dataDetail" />
                </el-col>
            </el-row>
            <el-form class="check-section">
                <div v-for="option in errorTypeOptions" :key="option" class="error-type-item">
                    <div class="error-type-label">{{ option }}：</div>
                    <el-radio-group :value="errorTypeStates[option]" class="error-type-buttons" @input="updateErrorTypeState(option, $event)">
                        <el-radio-button :label="false" class="right-radio" size="small">正确</el-radio-button>
                        <el-radio-button :label="true" class="error-radio" size="small">错误</el-radio-button>
                    </el-radio-group>
                </div>
            </el-form>
        </div>
        <div class="ut-edit-footer">
            <div class="surplus-info">
                <div v-if="form.suplus > 0" class="surplus-text">
                    剩余
                    <span class="surplus-count">{{ form.suplus }}</span>
                    份未校对
                </div>
                <div v-else class="surplus-text">暂无剩余校对任务</div>
            </div>
            <el-button type="primary" :loading="loading" @click="save">保存</el-button>
            <el-button @click="$emit('close')">关闭</el-button>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ProjectList from './project-list.vue'
import Vue from 'vue'
import DataSet from './data-set.vue'

export default {
    components: {
        DataSet,
        ProjectList,
    },
    props: {
        height: {
            type: Number,
            default: () => Vue.prototype.$baseTableHeight(-1),
        },
        parentData: {
            type: Object,
            default: () => ({}),
        },
        month: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            loading: false,
            taskData: {},
            form: {
                id: '',
                workDate: '',
                groupName: '',
                attendantId: '',
                attendantName: '',
                customerId: '',
                name: '',
                sex: 0,
                phone: '',
                idcard: '',
                address: '',
                projects: [],
                datas: [],
                suplus: 0,
                proofreadError: false,
                proofreadErrorRemark: '',
            },
            originalProjects: [],
            errorTypeOptions: ['签字表时间', '签字表项目', '项目时长'],
            errorTypeStates: {},
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        childData() {
            return {
                workId: this.form.id || this.parentData.id,
                customerId: this.form.customerId || this.parentData.customerId,
                workDate: this.form.workDate || this.parentData.workDate,
            }
        },
        selectedErrors: {
            get() {
                if (!this.form.proofreadError) {
                    return []
                }
                return this.form.proofreadErrorRemark?.trim().split('、') ?? []
            },
            set(val) {
                this.form.proofreadErrorRemark = val?.filter(Boolean).join('、') ?? ''
            },
        },
        dataDetail() {
            if (!this.form.datas?.length) return null
            const titles = []
            const files = []
            this.form.datas.forEach((data) => {
                titles.push(data.title)
                data.details?.forEach((detail) => {
                    files.push(...(detail.files || []))
                })
            })
            return {
                title: titles.join('、'),
                files,
            }
        },
    },
    mounted() {
        const workId = this.parentData?.id
        this.loadData(workId)
    },
    methods: {
        baseTableHeight(i) {
            return `${this.$baseTableHeight(i)}px`
        },
        async loadData(workId = null) {
            this.loading = true
            try {
                const { data } = await this.$ut.api('homecarelong/care/work/proofread/task', {
                    communityId: this.comm.id,
                    workId,
                    month: this.month,
                })
                this.updateData(data)
            } finally {
                this.loading = false
            }
        },
        updateData(data) {
            this.taskData = data
            this.form = {
                ...this.form,
                ...data,
                projects: data.projects || [],
                datas: data.datas || [],
                suplus: data.suplus || 0,
            }
            this.originalProjects = JSON.parse(JSON.stringify(data.projects || []))
            this.initErrorTypeStates()
        },
        initErrorTypeStates() {
            const errorStates = {}
            this.errorTypeOptions.forEach((option) => {
                errorStates[option] = false
            })
            if (this.form.proofreadErrorRemark) {
                const selectedErrors = this.form.proofreadErrorRemark
                    .trim()
                    .split('、')
                    .map((e) => e.replaceAll('有误', ''))
                selectedErrors.forEach((error) => {
                    if (this.errorTypeOptions.includes(error)) {
                        errorStates[error] = true
                    }
                })
                this.errorTypeOptions.forEach((option) => {
                    if (errorStates[option] === null && this.form.proofreadErrorRemark) {
                        errorStates[option] = false
                    }
                })
            }
            this.errorTypeStates = errorStates
        },
        updateErrorTypeState(option, value) {
            this.$set(this.errorTypeStates, option, value)
            this.syncErrorRemark()
        },
        syncErrorRemark() {
            const errorTypes = this.errorTypeOptions.filter((option) => this.errorTypeStates[option] === true)
            this.form.proofreadErrorRemark = errorTypes.map((e) => e + '有误').join('、')
            this.form.proofreadError = errorTypes.length > 0
        },
        updateProjects(newProjects) {
            const projectMap = new Map()
            newProjects.forEach((project) => {
                projectMap.set(project.projectId, project)
            })
            this.form.projects = Array.from(projectMap.values())
        },
        resetProjects() {
            this.form.projects = JSON.parse(JSON.stringify(this.originalProjects))
        },
        async save() {
            this.loading = true
            try {
                const params = {
                    communityId: this.comm.id,
                    workId: this.form.id,
                    month: this.month,
                    projectIds: this.form.projects?.map((p) => p.projectId) || [],
                    proofreadError: this.form.proofreadError,
                }
                if (this.form.proofreadError) {
                    params.proofreadErrorRemark = this.form.proofreadErrorRemark
                }
                const { data } = await this.$ut.api('homecarelong/care/work/proofread/saveTask', params)
                this.$baseMessage('保存成功！', 'success', 'ut-hey-message-success')
                this.$emit('fetchData')
                data ? this.updateData(data) : this.$emit('close')
            } finally {
                this.loading = false
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.box-wrapper {
    overflow-y: auto;
}

.customer-info {
    padding-bottom: 8px;
    line-height: 1.5;
}

.project-section {
    .section-header {
        display: flex;
        align-items: center;
        gap: 10px;

        .section-title {
            font-weight: bold;
            font-size: 16px;
        }
    }
}

.check-section {
    margin-top: 10px;
    margin-left: 20px;
    margin-right: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 16px;

    .error-type-item {
        display: flex;
        align-items: center;
    }

    :deep(.el-radio-button__inner) {
        box-shadow: none;
    }

    .right-radio {
        :deep(.el-radio-button__inner:hover) {
            color: $base-color-green;
        }

        :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
            color: $base-color-white;
            background-color: $base-color-green;
            border-color: $base-color-green;
        }
    }

    .error-radio {
        :deep(.el-radio-button__inner:hover) {
            color: $base-color-red;
        }

        :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
            color: $base-color-white;
            background-color: $base-color-red;
            border-color: $base-color-red;
        }
    }
}

.surplus-info {
    display: inline-block;
    margin-right: 20px;

    .surplus-text {
        font-size: 14px;

        .surplus-count {
            font-weight: bold;
            color: $base-color-red;
        }
    }
}
</style>
