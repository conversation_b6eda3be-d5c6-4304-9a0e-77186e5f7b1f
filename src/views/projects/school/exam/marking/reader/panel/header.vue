<template>
	<div class="reader-header">
		<div class="website-logo">
			<img src="../../../image/logo.png" />
			<cite>智校云阅卷</cite>
		</div>
		<div class="marking-info">
			<div>
				<span v-if="marking.gradeType==1">小学</span>
				<span v-else-if="marking.gradeType==2">初中</span>
				<span v-else-if="marking.gradeType==3">高中</span>
				<span>{{marking.grade}}级</span>
				<ut-icon icon="arrow-right-s-line" />
				<span>{{marking.courseName}}</span>
				<ut-icon icon="arrow-right-s-line" />
				<span class="score">{{marking.title}}（{{marking.score}}分）</span>
			</div>
		</div>
<!--        <router-link :to="{path: '/exam/marking/marking-list',query: {examId: examId,courseId: item.id,title: encodeURI(title),courseName: encodeURI(item.name)}}">-->
<!--            <el-button round icon="el-icon-back" size="mini" type="primary" plain style="padding: 4px 4px;margin: 0 8px">-->
<!--                <span>考试列表</span>-->
<!--            </el-button>-->
<!--        </router-link>-->
		<div v-if="!task.thanks && !task.stop" class="zoom-wrapper">
			<el-button round icon="el-icon-zoom-out" size="mini" type="primary" plain :disabled="isMinZoom" title="缩小 (Ctrl + -)" @click="zoom('in')">
				<span class="in">缩小</span>
			</el-button>

			<div class="zoom-slider-container">
				<el-slider v-model="sliderValue" :min="minSliderValue" :max="maxSliderValue" :step="sliderStep" :show-tooltip="false" class="zoom-slider" @change="handleSliderChange"/>
			</div>

			<el-button round icon="el-icon-zoom-in" size="mini" type="primary" plain :disabled="isMaxZoom" title="放大 (Ctrl + +)" @click="zoom('out')">
				<span class="out">放大</span>
			</el-button>

			<el-button round icon="el-icon-refresh" size="mini" type="primary" plain title="重置缩放 (Ctrl + 0)" style="margin-left: 0;" @click="resetZoom">
				<span class="reset">重置</span>
			</el-button>
		</div>
		<div v-if="!task.thanks && !task.stop" class="paper-tool">
			<el-button icon="el-icon-document" size="mini" @click="paperTestClick()">看全卷</el-button>
			<el-button icon="el-icon-notebook-2" @click="paperAnswerClick()">看答案</el-button>
		</div>
		<div class="user-login">
			<div class="user-nickname">
				<img v-if="user.headimgurl" :src="user.headimgurl" />
				<i v-else class="el-icon-s-custom icon-user" />
				<span class="user-name">{{ nickname }}</span>
				<i class="el-icon-caret-bottom icon-bottom" style="color: #7c7c7c" />
				<i class="el-icon-caret-top icon-top" />
			</div>
			<div class="user-setting">
				<div style="white-space: nowrap">
					<div class="setting-title" @click="$router.push('/my')">个人中心</div>
					<div class="setting-title" @click="$router.push('/exam/marking/exam-list')">首页</div>
					<div class="setting-title" @click="loginOutHandle">退出</div>
				</div>
			</div>
		</div>

		<my-paper-test :visible.sync="showPaperTest" :title="paperTitle" :paper-list="paperTest" />
	</div>
</template>

<script>
	import MyPaperTest from './my-paper-test.vue';
	import { mapState, mapActions, mapGetters } from 'vuex'

	export default {
		components: {
			MyPaperTest,
		},

		props: {
			marking: {
				type: Object,
				default: function () {
					return {}
				},
			},
			task: {
				type: Object,
				default: () => {
					return {}
				},
			},
			paperID: {
				type: Number,
				default: 0,
			},
			currentScale: {
				type: Number,
				default: 1,
			},
			minScale: {
				type: Number,
				default: 0.5,
			},
			maxScale: {
				type: Number,
				default: 3.0,
			},
		},
		data() {
			return {
				showPaperTest: false,
				paperTitle: '',
				paperTest: [
					{
						scale: 1,
						url: '',
					},
				],
			}
		},
		computed: {
			...mapGetters({
				nickname: 'user/nickname',
			}),
			...mapState({
				user: (state) => state.user,
			}),
			// 缩放相关计算属性
			zoomPercentage() {
				return Math.round(this.currentScale * 100) + '%'
			},
			isMaxZoom() {
				return this.currentScale >= this.maxScale
			},
			isMinZoom() {
				return this.currentScale <= this.minScale
			},
			minSliderValue() {
				return Math.round(this.minScale * 100)
			},
			maxSliderValue() {
				return Math.round(this.maxScale * 100)
			},
			sliderStep() {
				return 10 // 滑块步长为10%
			},
			sliderValue: {
				get() {
					return Math.round(this.currentScale * 100)
				},
				set(value) {
					const newScale = value / 100
					this.$emit('zoom', 'slider', newScale)
				},
			},
		},

		methods: {
			...mapActions({
				loginOut: 'user/logout',
			}),
			zoom(type) {
				this.$emit('zoom', type)
			},
			resetZoom() {
				this.$emit('zoom', 'reset')
			},
			handleSliderChange(value) {
				const newScale = value / 100
				this.$emit('zoom', 'slider', newScale)
			},
			markingSumScore() {
				let sum = 0
				if (this.marking.sub) {
					this.marking.sub.forEach((item) => {
						sum += item.scoreTotal
					})
				}
				return sum
			},
			gradeName(i) {
				switch (i) {
					case 1:
						return '小学'
					case 2:
						return '初中'
					case 3:
						return '高中'
				}
			},
			async loginOutHandle() {
				await this.loginOut()
				window.location = '/login'
			},
			paperTestClick() {
				this.paperTest = [
					// 这个数据根据传入paperID,api得到
					{
						url: 'https://oss.afjy.net/api/file/preview?file=aIcwACO.jpg',
						scale: 1,
					},
					{
						url: 'https://oss.afjy.net/api/file/preview?file=aIcwAC.jpg',
						scale: 1,
					},
				]
				this.paperTitle = '查看全卷'
				this.showPaperTest = true
			},
			paperAnswerClick() {
				this.paperTest = [
					// 这个数据根据传入paperID,api得到
                    {
                        url: 'https://oss.afjy.net/api/file/preview?file=aIcwACO.jpg',
                        scale: 1,
                    },
                    {
                        url: 'https://oss.afjy.net/api/file/preview?file=aIcwAC.jpg',
                        scale: 1,
                    },
				]
				this.paperTitle = '参考答案'
				this.showPaperTest = true
			},
		},
	}
</script>

<style lang="scss" scoped>
	.reader-header {
		background-color: #fff !important;
		height: 40px;
		-ms-user-select: none;
		-moz-user-select: none;
		user-select: none;
	}

	.website-logo {
		float: left;

		img {
			float: left;
			margin: 4px 0 0 4px;
			width: 30px;
		}

		cite {
			margin-left: 10px;
			float: left;
			font-size: 18px;
			font-weight: bold;
			line-height: 40px;
			font-style: normal;
			text-shadow: 3px 3px 4px #aaa;
		}
	}

	.marking-info {
		line-height: 40px;
		font-size: 16px;
		float: left;
		color: #777;
		margin-left: 20px;

		.score {
			font-weight: bold;
			color: #000;
		}
	}

	.zoom-wrapper {
		margin-top: 3px;
		font-size: 14px;
		line-height: 40px;
		display: inline-flex;
		align-items: center;
		gap: 8px;
		:deep(.el-button){
			padding: 4px 4px;
		}

		.zoom-slider-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 4px;

			.zoom-slider {
				width: 120px;
				margin: 0;

				:deep(.el-slider__runway) {
					height: 4px;
					background-color: #e4e7ed;
					border-radius: 2px;
				}

				:deep(.el-slider__bar) {
					height: 4px;
					background-color: #409eff;
					border-radius: 2px;
				}

				:deep(.el-slider__button) {
					width: 14px;
					height: 14px;
					border: 2px solid #409eff;
					background-color: #fff;
				}

				:deep(.el-slider__button:hover) {
					transform: scale(1.2);
				}
			}
		}
	}

	.paper-tool {
		display: inline;
		margin-left: 10px;
		font-size: 14px;
		line-height: 40px;
	}

	.user-login {
		float: right;
		margin-right: 10px;
		position: relative;
		padding-left: 16px;
		line-height: 40px;
		font-size: 14px;
		transition: all 0.3s;

		&:hover {
			cursor: pointer;
			color: #1fb0ff;
		}

		.user-name {
			padding: 0 10px;
		}

		.icon-top {
			display: none;
		}

		.icon-bottom {
			display: inline;
			color: #1fb0ff;
		}

		.user-nickname {
			display: flex;
			align-items: center;
			line-height: 40px;

			img {
				width: 26px;
				height: 26px;
				border-radius: 26px;
			}
		}

		.user-setting {
			border: 1px solid #1fb0ff;
			display: none;
			position: absolute;
			right: 0px;
			z-index: 9999;
			background: #fff;
			border-radius: 6px;
			box-shadow: 0 0 5px #ccc;

			.setting-title {
				color: #666;
				height: 40px;
				line-height: 40px;
				padding: 0px 60px 0px 12px;

				&:hover {
					cursor: pointer;
					color: #1fb0ff;
					background-color: #f5f5f5;

					&:first-child {
						border-radius: 6px 6px 0px 0px;
					}

					&:last-child {
						border-radius: 0px 0px 6px 6px;
					}
				}
			}
		}
	}

	.user-login:hover {
		.user-setting {
			display: block;
		}

		.icon-bottom {
			display: none;
		}

		.icon-top {
			display: inline;
			color: #1fb0ff;
		}

		.user-name {
			color: #1fb0ff;
		}

		.icon-user {
			color: #1fb0ff;
		}
	}
</style>
