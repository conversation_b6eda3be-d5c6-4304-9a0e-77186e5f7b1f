<template>
    <div class="marking-wrapper" @keydown="handleKeydown">
        <my-header
            ref="header"
            :marking="marking"
            :task="task"
            :paper-i-d="paper.ID"
            :current-scale="config.pageScale"
            :min-scale="minScale"
            :max-scale="maxScale"
            @zoom="handleZoom"
        />
        <my-work
            ref="work"
            :show-tool="config.showTool"
            :show-box="config.showBox"
            :one-key="config.oneKey"
            :calcModel="config.calcModel"
            :page-scale="config.pageScale"
            :task="task"
            :paper-scale="paper.scale"
            :url="paper.url"
            :flag.sync="flagData"
            :sub-marking="marking.sub"
            :show-my-paper="showMyPaper"
            :my-paper="myPaper"
            :loading="loading"
            :interval-time="marking.intervalTime"
            @collectionChange="collectionChange"
            @scoreMyChange="scoreMyChange"
            @submit="submit"
        />
        <my-footer
            :task="task"
            :paper-i-d="paper.ID"
            :show-tool="config.showTool"
            :show-box="config.showBox"
            :one-key="config.oneKey"
            :submit-tip="config.submitTip"
            :show-my-paper="showMyPaper"
            :task-count-all="progress.total"
            :task-complete-all="progress.complete"
            :task-count-my="progress.myTotal"
            :task-complete-my="progress.myComplete"
            :is-first="false"
            :is-last="false"
            @showToolChange="handleShowToolChange"
            @showBoxChange="handleShowBoxChange"
            @oneKeyChange="handleOneKeyChange"
            @submitTipChange="handleSubmitTipChange"
            @update:showMyPaper="showMyPaper = $event"
            @openJumpModal="openJumpModal"
        />

        <!-- 跳转到弹窗 -->
        <el-dialog
            v-if="showJumpModal"
            v-dialogDrag
            :close-on-click-modal="false"
            title="跳转到"
            :visible.sync="showJumpModal"
            width="800px"
            append-to-body
            @close="closeJumpModal"
        >
            <jump
                :reviewed-papers="reviewedPapers"
                :jump-paper-number.sync="jumpPaperNumber"
                :current-paper-id="paper.ID"
                @jump-to-paper="jumpToPaper"
                @view-full-paper="viewFullPaper"
            />
        </el-dialog>
    </div>
</template>
<script>
import Vue from 'vue'
import { Modal } from 'ant-design-vue'

Vue.use(Modal)
import myHeader from './panel/header.vue'
import myFooter from './panel/footer.vue'
import myWork from './work'
import Jump from './panel/jump.vue'

export default {
    components: {
        myHeader,
        myFooter,
        myWork,
        Jump,
    },
    props: {},
    data() {
        return {
            loading:false,
            loadOver:false,
            showMyPaper: false,
            showJumpModal: false, // 控制跳转弹窗显示
            jumpPaperNumber: '', // 输入的卷号
            minScale: 0.1, // 最小缩放比例
            maxScale: 4.0, // 最大缩放比例
            scaleStep: 0.1, // 缩放步长
            config: {
                showTool: true, // 显示批注工具条
                showBox: true, // 显示给分按键面板
                oneKey: true, // 一键给分
                submitTip: true, // 打分提示
                calcModel: true, //true 累加模式  false:累减模式   *** 功能完成还没有设置界面
                pageScale: 1,
            },
            task: {
                thanks: false,
                pause: false,
                stop: false,
                allowtime: [],
                stoptime: [],
            },
            marking: { },
            flagData:[],
            paper: {},
            progress: {},
            myPaper: {
                RecordMax: 100,
                PageSize: 10,
                PageMax: 10,
                CurrPage: 1,
                Info: [],
            },
            reviewedPapers: [
                {
                    paperNumber: '2341',
                    submitTime: '2024-01-15 14:30:25',
                    score: '8.5',
                    status: 'completed',
                },
                {
                    paperNumber: '2340',
                    submitTime: '2024-01-15 14:25:18',
                    score: '7.0',
                    status: 'completed',
                },
                {
                    paperNumber: '2339',
                    submitTime: '2024-01-15 14:20:12',
                    score: '6.5',
                    status: 'completed',
                },
                {
                    paperNumber: '2338',
                    submitTime: '2024-01-15 14:15:08',
                    score: '9.0',
                    status: 'completed',
                },
                {
                    paperNumber: '2337',
                    submitTime: '2024-01-15 14:10:03',
                    score: '5.5',
                    status: 'completed',
                },
            ],
        }
    },
    computed: {
        navList() {
            return []
        },
    },
    watch: {
        // 监听弹窗关闭，清空输入框
        showJumpModal(newVal) {
            if (!newVal) {
                this.jumpPaperNumber = ''
            }
        },
    },
    mounted() {
        this.$el.setAttribute('tabindex', '0')
        this.$el.focus()
    },
    created(){
        this.getMarkingDetail()
        this.getMarkingMyTask()
    },
    methods: {
        handleZoom(type, customScale) {
            let newScale = this.config.pageScale
            if (type === 'out') {
                newScale = Math.min(this.maxScale, this.config.pageScale + this.scaleStep)
            } else if (type === 'in') {
                newScale = Math.max(this.minScale, this.config.pageScale - this.scaleStep)
            } else if (type === 'reset') {
                newScale = 1
            } else if (type === 'slider') {
                newScale = Math.max(this.minScale, Math.min(this.maxScale, customScale))
            }
            newScale = Math.round(newScale * 100) / 100
            if (newScale !== this.config.pageScale) {
                this.config.pageScale = newScale
            }
        },
        handleKeydown(event) {
            // Ctrl + 加号 或 Ctrl + 等号 放大
            if ((event.ctrlKey || event.metaKey) && (event.key === '+' || event.key === '=')) {
                event.preventDefault()
                this.handleZoom('out')
            }
            // Ctrl + 减号 缩小
            else if ((event.ctrlKey || event.metaKey) && event.key === '-') {
                event.preventDefault()
                this.handleZoom('in')
            }
            // Ctrl + 0 重置缩放
            else if ((event.ctrlKey || event.metaKey) && event.key === '0') {
                event.preventDefault()
                this.resetZoom()
            }
        },
        // 重置缩放
        resetZoom() {
            if (this.config.pageScale !== 1) {
                this.config.pageScale = 1
            }
        },

        // 处理显示切换事件
        handleShowToolChange(value) {
            this.config.showTool = value
        },
        handleShowBoxChange(value) {
            this.config.showBox = value
        },
        handleOneKeyChange(value) {
            this.config.oneKey = value
        },
        handleSubmitTipChange(value) {
            this.config.submitTip = value
        },
        collectionChange(v) {
            this.paper.collection = v
        },
        scoreMyChange(v) {
            this.marking.sub = v
            // console.log(this.marking.sub)
        },
        submit(callback) {
            let _this = this
            if (this.config.submitTip) {
                const h = this.$createElement
                let div = h('div', { style: { textAlign: 'center' } }, [
                    h('div', null, [h('span', { style: { fontSize: '80px', color: '#ff0000' } }, _this.getScoreMySum()), h('span', null, ' 分')]),
                    h('div', { style: { fontSize: '16px' } }, '确定继续提交吗？'),
                    h('div', { style: { fontSize: '12px', color: '#333' } }, '(不想再弹出此提示窗，可以点击底部“打分提示”取消)'),
                    h('div', { style: { fontSize: '12px', color: '#ccc' } }, '(关闭窗体可以按Esc键，继续提交可以按回车键(Enter))'),
                ])
                Modal.confirm({
                    content: div,
                    okText: '继续提交',
                    cancelText: '取消',
                    mask: true,
                    zIndex: 2000,
                    onOk() {
                        _this.postPaperData()
                        _this.paper.flag = []
                        callback(true)
                    },
                    onCancel() {
                        callback(false)
                    },
                })
            } else {
                _this.postPaperData()
                callback(true)
            }
        },
        getScoreMySum() {
            return this.$refs.work.getScoreMySum()
        },
        getSubScore(){
            return this.$refs.work.getSubScore()
        },
        openJumpModal() {
            this.showJumpModal = true
            this.jumpPaperNumber = ''
        },
        closeJumpModal() {
            this.showJumpModal = false
        },
        jumpToPaper(paperNumber) {
            console.log('跳转到卷号:', paperNumber)
            this.$message.success(`正在跳转到卷号 ${paperNumber}`)
            this.showJumpModal = false
        },
        viewFullPaper(paper) {
            console.log('查看全卷:', paper)
            this.$refs.header.paperTestClick()
        },
        async getMarkingDetail(){
            const markingId=this.$route.query.markingId
            if(!markingId) return
            const {data} = await this.$ut.api('schoolmarking/marking/detail',{
                markingId:markingId
            })
            this.marking=data
        },
        async getMarkingMyTask(){
            const markingId=this.$route.query.markingId
            if(!markingId) return
            const {data} = await this.$ut.api('schoolmarking/marking/myTask',{
                markingId:markingId
            })
            this.progress=data
            this.task.thanks=data.thanks
            this.task.stop=data.stop;
            // if(this.progress.complate >=this.progress.total && this.progress.total>0){
            //     this.task.thanks=true
            //     return
            // }

           this.getMarkingMyNew()
        },
        async getMarkingMyNew(){
            const markingId=this.$route.query.markingId
            if(!markingId) return
            this.loading=true
            const {data} = await this.$ut.api('schoolmarking/marking/myNew',{
                markingId:markingId
            }).finally(()=>{this.loading=false})
            this.paper=data
            this.flagData=[]

            // let timestamp=Date.now()
            // this.paper.url[0]='https://www.ebyte.com/Uploadfiles/Picture/2025-1-22/20251221745549631.jpg'+`?timestamp=${timestamp}`

        },
        async postPaperData() {
            const sub=this.getSubScore()
            const reqData={
                markingId:this.$route.query.markingId,
                paperId:this.paper.id,
                score:this.getScoreMySum(),
                flags:this.flagData,
                subs:!sub || sub.length<=1?[]:sub,
            }
            this.flagData=[]
            this.marking.sub.forEach(sub=>{
                this.$set(sub,'scoreMy','')
            })
            this.loading=true
            this.$ut.api('schoolmarking/marking/myAssign',reqData).finally(()=>{this.loading=false}).then((res)=>{
                if(res.data.refresh){
                    this.getMarkingMyNew()
                    return;
                }
                this.$set(this.progress,'total',res.data.total)
                this.$set(this.progress,'complete',res.data.complete)
                this.$set(this.progress,'myComplete',res.data.myComplete)
                this.$set(this.progress,'myTotal',res.data.myTotal)

                if(res.data.over){
                    this.task.thanks=res.data.over
                    return;
                }
                this.getMarkingMyNew()
            })

            this.paper={}


        },
    },
}
</script>

<style>
[aria-hidden='true'][tabindex='0'] {
    visibility: hidden !important;
    pointer-events: none !important;
}

.ant-modal-confirm-btns {
    text-align: center;
    width: 100%;
}
</style>
<style lang="scss" scoped>
@import '~ant-design-vue/dist/antd.css';

.marking-wrapper {
    height: 100vh;
    clear: both;
    -ms-user-select: none;
    -moz-user-select: none;
    user-select: none;
    outline: none; /* 移除焦点时的边框 */
}
</style>
