<template>
	<div class="work-box">
		<div class="work">
			<my-thanks v-if="task.thanks" />
			<my-pause v-if="task.pause" />
			<my-stop v-if="task.stop" :task="task" />
			<my-left-tool v-if="!task.thanks && !task.pause" :show-tool="showTool" :tool="tool" @toolChange="toolChange" />
			<my-key-board
				v-if="!task.thanks"
				ref="key_board"
				:image-loaded="imageLoaded"
				:marking-index="currMarkingindex"
				:sub-marking="sub"
				:show-collection="isCollection"
				:one-key="oneKey"
				@markingIndexChange="markingIndexChange"
				@collectionChange="collectionChange"
				@scoreMyChange="scoreMyChange"
				@submit="submit"
			/>
			<my-mouse-board
				v-if="!task.thanks"
				:show-box="showBox"
				:image-loaded="imageLoaded"
				:score="getSubScoreTotal()"
				:step-score="sub[currMarkingindex]?.step"
				@keyClick="keyClick"
				@stepScoreChange="stepScoreChange"
			/>
			<my-image
				v-if="!task.thanks"
				:marking-index="currMarkingindex"
				:tool="tool"
				:step-score="sub[currMarkingindex]?.step"
				:calcModel="calcModel"
				:page-scale="pageScale"
				:paper-scale="paperScale"
				:url="url"
				:rect="getRect()"
				:flag.sync="flagData"
				:show-annotations="showTool"
				:sub-marking="sub"
				@markingIndexChange="markingIndexChange"
				@markingScoreMethod="markingScoreMethod"
				@markingScoreMethodByFlag="markingScoreMethodByFlag"
				@imageLoadedChange="imageLoadedChange"
			/>
		</div>
	</div>
</template>
<script>
	import { decimalsAdd } from './components/operation'
	import MyThanks from './components/thanks.vue'
	import MyPause from './components/pause.vue'
	import MyStop from './components/stop.vue'
	import MyLeftTool from './components/leftTool.vue'
	import MyKeyBoard from './components/keyboard.vue'
	import MyMouseBoard from './components/mouseboard.vue'
	import MyImage from './components/image.vue'

	export default {
		components: {
			MyThanks,
			MyPause,
			MyStop,
			MyLeftTool,
			MyKeyBoard,
			MyMouseBoard,
			MyImage,
		},
		props: {
			task: {
				type: Object,
				default: () => {
					return {}
				},
			},
			pause: {
				type: Boolean,
				default: false,
			},
			showBox: {
				type: Boolean,
				default: false,
			},
			showTool: {
				type: Boolean,
				default: false,
			},
			oneKey: {
				type: Boolean,
				default: false,
			},
			calcModel: {
				type: Boolean,
				default: false,
			},
			showCollection: {
				type: Boolean,
				default: false,
			},
			subMarking: {
				type: Array,
				default: () => {
					return []
				},
			},
			pageScale: {
				type: Number,
				default: 1,
			},
			paperScale: {
				type: Number,
				default: 1,
			},
			url: {
				type: Array,
				default: function () {
					return []
				},
			},
			flag: {
				type: Array,
				default: function () {
					return []
				},
			},
			showMyPaper: {
				type: Boolean,
				default: false,
			},
			myPaper: {
				type: Object,
				default: () => {
					return {}
				},
			},

			intervalTime:{
				type:Number,
				default:0,
			}
		},
		data() {
			return {
				isTool: this.showTool,
				tool: '',
				imageLoaded: false,
				isCollection: this.showCollection,
				currMarkingindex: -1,
				step: '1',
				sub: [],
				flagData:this.flag,
			}
		},
		watch:{
			subMarking:{
				deep:true,
				handler(v){
					this.sub=v
				}
			},
			flag:{
				deep:true,
				handler(v){
					this.flagData=v
				}
			},

			flagData:{
				deep:true,
				handler(v){
					this.$emit('update:flag',v)
				}
			}
		},
		created(){
			this.sub=this.subMarking
		},
		methods: {
			imageLoadedChange(v) {
				if(!v)this.imageLoaded=false
				if(this.intervalTime>0){
					setTimeout(() => {
						this.imageLoaded = v
					}, this.intervalTime*1000);
				}else{
					this.imageLoaded=v
				}

			},
			markingIndexChange(i) {
				this.currMarkingindex = i
			},
			getSubScoreTotal() {
				let curMarking = this.subMarking[this.currMarkingindex]
				if (!curMarking) return 0

				return curMarking.scoreTotal
			},
			collectionChange(v) {
				this.isCollection = v
				this.$emit('collectionChange', v)
			},
			scoreMyChange(v) {
				this.sub = v
				this.$emit('scoreMyChange', v)
			},
			submit() {
				return this.$emit('submit', this.submitCallBack)
			},
			getSubmitState() {
				for (let i = 0; i < this.sub.length; i++) {
					if (this.sub[i].scoreMy == '' || this.sub[i].scoreMy == null || this.sub[i].scoreMy == undefined) {
						return false
					} else if (parseFloat(this.sub[i].scoreMy) > this.sub[i].scoreTotal) return false
				}
				return true
			},
			submitCallBack(v) {
				if (v) {
					this.currMarkingindex = 0
					this.$refs.key_board.panelClick()
				} else {
					this.$refs.key_board.panelClick()
				}
			},
			keyClick(v) {
				if (this.currMarkingindex < 0 || this.sub.length <= 0) return
				let item = this.sub[this.currMarkingindex]
				if (v == 'full') {
					item.scoreMy = item.scoreTotal
				} else {
					item.scoreMy = v
				}
				this.$emit('scoreMyChange', this.sub)
				if (this.oneKey) {
					if (this.currMarkingindex < this.sub.length - 1) {
						this.currMarkingindex++
					} else {
						if (this.getSubmitState()) {
							this.submit()
						}
					}
				}
			},
			toolChange(v) {
				this.tool = v
                if (v === 'clear') {
                    // todo 弹窗确认清空标记
                }
				// if(v=="error"){
				//     let isBreak=false
				//     for(let i=0;i<this.sub.length;i++){
				//         if(this.sub[i].scoreMy!=''){
				//             isBreak=true
				//             return true
				//         }
				//     }
				//     if(isBreak) return
				//     for(let i=0;i<this.sub.length;i++){
				//         this.sub[i].scoreMy=this.sub[i].scoreTotal
				//     }
				// }
			},
			stepScoreChange(v) {
				// this.step = v
				if(this.currMarkingindex<0) return
				this.sub[this.currMarkingindex].step=v
			},
			markingScoreMethod(data) {
				if (this.currMarkingindex < 0 || this.sub.length <= 0) return
				let item = this.sub[data.index]
				if (data.type == 'right' || data.type == 'error') {
					item.scoreMy = decimalsAdd(parseFloat(item.scoreMy || 0) + parseFloat(data.caption || 0))
				}
				if (item.scoreMy && item.scoreMy <= 0) item.scoreMy = 0
				this.$emit('scoreMyChange', this.sub)
			},
			markingScoreMethodByFlag(data, oldValue, tool) {
				if (this.currMarkingindex < 0 || this.sub.length <= 0) return
				let item = this.sub[data.index]
				if (!item.scoreMy) return
				if (tool == 'right' || tool == 'error') {
					item.scoreMy = decimalsAdd(parseFloat(item.scoreMy || 0), -oldValue)
					item.scoreMy = decimalsAdd(parseFloat(item.scoreMy || 0) + parseFloat(data.caption || 0))
				} else if (tool == 'cut') {
					item.scoreMy = decimalsAdd(parseFloat(item.scoreMy || 0), -oldValue)
				}
				if (item.scoreMy && item.scoreMy <= 0) item.scoreMy = 0
				this.$emit('scoreMyChange', this.sub)
			},
			getRect() {
				let arr = []
				for (let i = 0; i < this.subMarking.length; i++) {
					arr.push(this.subMarking[i].rect)
				}
				return arr
			},
			getScoreMySum(){
				return this.$refs.key_board.getScoreMySum()
			},
			getSubScore(){
				return this.$refs.key_board.getSubScore()
			}
		},
	}
</script>
<style lang="scss" scoped>
	.work-box {
		clear: both;
		height: calc(100% - 80px);
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: flex-start;
		position: relative;
		background-color: #656565;
		overflow: hidden;
	}

	.work {
		min-width: 0;
		flex: 1;
		height: 100%;
		width: 100%;
		background-color: #636363;
		position: relative;
	}

	.left-tool {
		float: left;
	}
</style>
