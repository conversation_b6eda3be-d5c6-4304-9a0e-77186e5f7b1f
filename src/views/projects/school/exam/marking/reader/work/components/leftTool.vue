<template>

<VueDragResize
    v-if="showTool" 
    :is-draggable="true"
    :is-resizable="false" 
    :parent-limitation="true" 
    :x="left" :y="top" w="auto" h="auto" :z="1006" 
    :is-active="false"
    style="outline:none;"
    drag-handle=".tool .drag,.tool .drag i" @resizing="resize"
    @dragging="resize"
    >
    <div class="tool">
        <div class="pull-left drag">
            <i class="en-icon el-icon-more"></i>
        </div>
        <div class="item" :class="tool=='right'?'selected':''" @click="itemClick('right')">
            <i class="el-icon-check" title="正确累加模式"  />
        </div>
        <div class="item" :class="tool=='error'?'selected':''" @click="itemClick('error')">
            <i class="el-icon-close" title="错误累减模式"  />
        </div>
        <div class="item" :class="tool=='text'?'selected':''" @click="itemClick('text')">
            <i class="el-icon-edit" title="写入批语"  />
        </div>
        <!-- <div class="item" :class="tool=='line'?'selected':''" @click="itemClick('line')">
            <a-icon type="line" title="勾画重点" />
        </div> -->
        <div class="item" :class="tool=='cut'?'selected':''" @click="itemClick('cut')">
            <i class="el-icon-scissors" title="删除标注"  />
        </div>
        <div class="item" @click="itemClick('clear')">
            <i class="el-icon-delete" title="清空所有标注"  />
        </div>
    </div>
</VueDragResize>
   
</template>

<script>

import VueDragResize from '@/extra/vue-drag-resize'

export default {
    components: {
        VueDragResize,
    },
    directives: {
        // drag: {
        //     // 指令的定义
        //     bind: function (el) {
        //         let odiv = el   //获取当前元素
        //         odiv.onmousedown = (e) => {
        //             //算出鼠标相对元素的位置
        //             let disX = e.clientX - odiv.parentNode.offsetLeft;
        //             let disY = e.clientY - odiv.parentNode.offsetTop;
                    
        //             document.onmousemove = (e)=>{
        //                 //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
        //                 let left = e.clientX - disX;    
        //                 let top = e.clientY - disY;
        //                 if(top<=48) return;
        //                 if(left<=0) return;
        //                 if(top+odiv.parentNode.offsetHeight+32>=document.body.clientHeight) return;
        //                 if(left+odiv.parentNode.offsetWidth>=document.body.clientWidth) return;
                      
        //                 //绑定元素位置到positionX和positionY上面
        //                // this.positionX = top;
        //                // this.positionY = left;
                
        //                 //移动当前元素
        //                 odiv.parentNode.style.left = left + 'px';
        //                 odiv.parentNode.style.top = top + 'px';
        //                 odiv.parentNode.style.bottom = 'none';
        //             };
        //             document.onmouseup = () => {
        //                 document.onmousemove = null;
        //                 document.onmouseup = null;
        //             };
        //         };
        //     }
        // }
    },
    props:{
        showTool:{
            type:Boolean,
            default:false
        },
        tool:{
            type:String,
            default:''
        }
    },
    data(){
        return{
            width: 0,
            height: 0,
            top: 0,
            left: 0,
        }
    },
    created(){
        let width=parseInt(document.body.clientWidth) - 350
        let _left=sessionStorage.getItem("tool-left") || width
        let _top=sessionStorage.getItem("tool-top") || 5
        _left=parseInt(_left)
        _top=parseInt(_top)
        this.left = _left
        this.top = _top
    },
    methods:{
         resize(newRect) {
                // this.width = newRect.width;
                // this.height = newRect.height;
                this.top = newRect.top;
                this.left = newRect.left;
                sessionStorage.setItem("tool-left",this.left)
                sessionStorage.setItem("tool-top",this.top)
        },
        itemClick(t){
            if(t!=this.tool && t!=''){ this.$emit("toolChange",t)}
            else{ this.$emit("toolChange",'')}
        }
    }
}
</script>

<style lang="scss" scoped>
    .tool{
        z-index: 100;
        min-width: 260px;
        position: absolute;
        /* left: 0.5rem; */
        height: 40px;
        background: #2f4050;
        color: #ccc;
        border-radius: 5px;
        box-shadow:0 0 5px #888;
        padding: 2px 5px 0 5px;
        overflow: hidden;
        transition: all .1s ease-in 0ms;
    }

    .tool .drag{
        float: left;
        padding: 8px 0;
        cursor: move;
        color: orange;
        width:20px;
        transform: rotate(90deg);
    }

    .tool .drag i{
        padding: 0 !important;
    }


    .tool .item{
        float:left;
        border-radius: 4px;
        margin-right: 4px;
    }

    .tool i{
        font-size: 20px;
        padding:8px;
    }

    .tool .item:hover{
         background: #4b5864;
    }

    .item.selected{
        background: #637281 !important;
    }
    .item.selected i{
        color:gold;
    }

.vdr.active:before {
    outline: none;
}
</style>