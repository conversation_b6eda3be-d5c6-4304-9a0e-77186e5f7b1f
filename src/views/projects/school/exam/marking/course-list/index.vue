<template>
	<div >
        <exam-header />
        <top :nav-list="navList" />
		<div class="wrapper">
			<list :data-list="dataList" :title="title" :exam-id="examId"/>
		</div>
	</div>
	
</template>

<script>
import { mapGetters } from 'vuex'
import ExamHeader from '@/custom/layouts/ut-exam/exam-header.vue'
import Top from '../components/top.vue'
import List from './list.vue'

export default {
	components:{
        ExamHeader,
		Top,
		List,
	},
	props: {

	},
	data() {
		return {
			navList:[],
			queryForm:{
				pagesize:20,
				pageindex:1,
			},
			dataList:[],
			title:'',
			examId:'',
			
		}
	},
	computed: {
		...mapGetters({
			// school: 'comm/comm',
		}),
	},
	created() {
		this.examId=this.$route.query.examId
		this.initTitle()
		this.fetchData()
	},
	methods: {
		handleSizeChange(val) {
			this.queryForm.pagesize = val
			this.fetchData()
		},
		handleCurrentChange(val) {
			this.queryForm.pageindex = val
			this.fetchData()
		},
		initTitle(){
			this.title=decodeURI(this.$route.query.title)||''
			this.navList=[
				{
					name:'网络阅卷',
					url:'/exam/marking/exam-list'
				},
				{
					name:this.title,
					url:''
				},
			]
		},
		async fetchData(){
			this.listLoading = true
			const {data} = await this.$ut.api('schoolmarking/my/course', {
				examId:this.$route.query.examId
			}).finally(()=>{this.listLoading=false})
			this.dataList = data
		}
		
	
	},
}
</script>

<style scoped lang="scss">

.wrapper {
	width: 100%;
	min-width: 1350px;
	margin-top: 8px;
	padding: 16px;
	background-color: #fff;	

}
</style>
