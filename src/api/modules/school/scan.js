export default {
    package:{
        list:{
            url: '/exammanagement/scan/package_list',
            auth: true,
            method: 'GET',
        },
        paper:{
            list:{
                url: '/exammanagement/scan/package_paper_listpg',
                auth: true,
                method: 'GET',
            },
            info:{
                url: '/exammanagement/scan/package_paper_info',
                auth: true,
                method: 'GET',
            }
        },
        move:{
            url: '/exammanagement/scan/package_paper_move',
            auth: true,
            method: 'GET',
        },
        rotate:{
            url: '/exammanagement/scan/package_paper_spin',
            auth: true,
            method: 'GET',
        },
        flip:{
            url: '/exammanagement/scan/package_paper_turn',
            auth: true,
            method: 'GET',
        },
        delete:{
            url: '/exammanagement/scan/package_paper_delete',
            auth: true,
            method: 'GET',
        },
        
    },
    paper:{
        manualSave:{
            url: '/exammanagement/scan/virtual_save',
            auth: true,
            method: 'POST',
        },
        move:{
            url: '/exammanagement/scan/paper_move',
            auth: true,
            method: 'POST',
        },
        rotate:{
            url: '/exammanagement/scan/paper_spin',
            auth: true,
            method: 'POST',
        },
        flip:{
            url: '/exammanagement/scan/paper_turn',
            auth: true,
            method: 'POST',
        },
        delete:{
            url: '/exammanagement/scan/paper_delete',
            auth: true,
            method: 'POST',
        },
    },
    class:{
        list:{
            url: '/exammanagement/scan/class_list',
            auth: true,
            method: 'GET',
        }
    },
    room:{
        list:{
            url: '/exammanagement/scan/examRoom_list',
            auth: true,
            method: 'GET',
        }
    },
    student:{
        noScanList:{
            url: '/exammanagement/scan/noScan_student_listpg',
            auth: true,
            method: 'GET',
        }
    }
}